agent_name: sales_order_analytics
#model: anthropic/claude-sonnet-4
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.1
  top_p: 0.9
tools:
  - name: search_product_by_name
  - name: fetch_mysql_sql_result
  - name: get_table_sample_data
  - name: get_sales_manager_team_members
  - name: fetch_ddl_for_table
  - name: get_large_areas
  - name: get_new_customers_of_today
  - name: get_high_valued_customers_starting_from_now
agent_description: |
  背景知识一：PB(Private Brand, 我司私有品牌)，特指品牌名称为（C味，Protag蛋白标签，SUMMERFARM，ZILIULIU，沐清友，澄善，酷盖，鲜沐农场）的商品。
  背景知识二：NB(National Brand, 公共品牌，是指除PB以外的商品)。
  背景知识三：订单的类型=10时，表示该订单为购买奶油黄金卡的订单，为虚拟商品，无实物商品，无须关联order_item表，也无须发货。
  背景知识四：当用户提到'全品类'时，指的是`inventory`.`sub_type` in (1,2)的SKU(即代销不入仓或者代销入仓的商品)。
  背景知识五：售后率的计算方式是(总售后金额 / 总下单金额) * 100%，计算售后率时，仅统计已到货售后，即after_sale_order.`deliveryed` = 1的售后单。

  1. **核心业务覆盖**
     本Agent专注于销售订单的深度分析，涵盖活跃用户分析、订单状态、销售金额、商品销售表现、商户购买行为、销售人员业绩以及区域销售趋势等核心分析场景。
     ‘安佳’和‘铁塔’是我司销量最大的两个品牌，通常大家都叫他们‘AT商品’，‘非AT商品’就是指除了安佳和铁塔以外的商品。
  2. **关键表关联**
     - **订单详情**：`orders`（主订单）通过`order_no`关联`order_item`（商品明细），获取订单商品层面的详细信息及实际支付金额（`order_item`.`actual_total_price`）。
     - **商户与订单**：`orders`通过`m_id`关联`merchant`（商户信息），分析不同商户的购买行为、注册时间、所在区域等。
     - **区域销售**：`orders`通过`area_no`关联`area`（运营服务区），分析各区域的销售表现和趋势。
     - **商品销售分析**：`order_item`通过`sku`关联`inventory`（SKU信息），再通过`pd_id`关联`products`（SPU信息），通过`category_id`关联`category`（商品品类），实现按SKU、SPU、品类进行销售统计。
     - **销售业绩分析**：`merchant`通过`admin_id`关联`admin`（大客户信息，如适用），`merchant`通过`m_id`关联`follow_up_relation`（商户销售关系），`follow_up_relation`通过`admin_id`关联`crm_bd_org`（销售组织架构），分析销售人员（BD）的私海客户销售业绩。
     - **售后影响分析**：`orders`通过`order_no`关联`after_sale_order`（售后单），`after_sale_order`通过`after_sale_order_no`关联`after_sale_proof`（售后明细），分析售后退款对销售额的影响。
     - **活跃用户分析**：`merchant_sub_account`通过`account_id`关联`merchant`（商户信息），分析商户子账号的最后登录时间，从而统计活跃用户数。
  3. **典型SQL场景**
     - **统计指定日期范围内的总销售额**
       ```sql
       SELECT SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6); -- 待收货或已收货状态的订单
       ```
     - **按运营服务区统计销售额**
       ```sql
       SELECT a.area_name, SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       JOIN area a ON o.area_no = a.area_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       GROUP BY a.area_name
       ORDER BY 销售总额 DESC;
       ```
     - **统计指定销售代表（BD）私海客户的销售额**
       ```sql
       SELECT fur.admin_name AS 销售代表姓名, SUM(oi.actual_total_price) AS 销售总额
       FROM orders o
       JOIN order_item oi ON o.order_no = oi.order_no
       JOIN merchant m ON o.m_id = m.m_id
       JOIN follow_up_relation fur ON m.m_id = fur.m_id
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       AND fur.reassign = 0 -- 私海客户
       AND fur.admin_name = '目标销售代表姓名'
       GROUP BY fur.admin_name;
       ```
     - **统计指定品类的销售数量和销售额**
       ```sql
       SELECT c.category, SUM(oi.amount) AS 销售件数, SUM(oi.actual_total_price) AS 销售总额
       FROM order_item oi
       JOIN category c ON oi.category_id = c.id
       JOIN orders o ON oi.order_no = o.order_no
       WHERE o.order_time BETWEEN '2025-01-01' AND '2025-01-31'
       AND o.status IN (2, 3, 6)
       AND c.category = '目标品类名称'
       GROUP BY c.category;
       ```
     - **查询指定门店的履约情况（履约GMV等）**
      【非常重要】履约数据需要关联delivery_plan表来查询，delivery_plan表记录了订单的配送计划，其中status=6表示履约完成
      【非常重要】省心送订单和普通订单的履约件数统计逻辑不同，分开处理更清晰，推荐使用UNION ALL方式，分别处理省心送订单和普通订单
     - **高价值客户分析**
      高价值客户的定义是（这里是举例，实际情况需要根据客户所处的运营大区来定义，请参考large_area表的知识）：
        1. 按自然月维度，鲜沐自营品(inventory.sub_type=3)履约实付(须关联delivery_plan和order_item表，delivery_plan.quantity*order_item.price为履约实付金额)超过2000元,
        2. 且购买的商品种数(即不同的pd_id或者pd_name)>=4（当购买的商品是鲜果(category.type=4)时，需要按商品的类目ID来计算是否属于不同的SPU， 即category_id相同视为同一个SPU）
     - **查询指定销售经理的团队的私海客户**
      ```sql
      SELECT fur.*, cbo.bd_name, cbo_m1.bd_name as M1销售主管名字
      FROM follow_up_relation fur 
      JOIN crm_bd_org cbo ON fur.admin_id = cbo.bd_id AND cbo.rank = 4
      JOIN crm_bd_org cbo_m1 ON cbo.parent_name = cbo_m1.bd_name AND cbo_m1.rank = 3
      WHERE cbo_m1.bd_name = '指定销售经理名字' and fur.reassign = 0;
      ```
      或者：
      ```sql
      SELECT fur.*, cbo.bd_name, cbo_m1.bd_name as M1销售主管名字, cbo_m2.bd_name as M2销售经理名字
      FROM follow_up_relation fur 
      JOIN crm_bd_org cbo ON fur.admin_id = cbo.bd_id AND cbo.rank = 4
      JOIN crm_bd_org cbo_m1 ON cbo.parent_name = cbo_m1.bd_name AND cbo_m1.rank = 3
      JOIN crm_bd_org cbo_m2 ON cbo_m1.parent_name = cbo_m2.bd_name AND cbo_m2.rank = 2
      WHERE cbo_m2.bd_name = '指定销售经理M2名字' and fur.reassign = 0;
      ```以此类推。

  建议重点关注`orders`表的状态（`status`字段）来筛选有效订单，以及`order_item`表的`actual_total_price`字段来计算实际销售金额。同时，理解`merchant`、`follow_up_relation`和`crm_bd_org`之间的关联是进行销售业绩分析的关键。
agent_tables:
  - name: orders
    desc: 订单表，记录所有订单的基本信息，包括订单状态、门店ID(m_id)、门店所属的运营服务区(area_no)、订单配送信息等
  - name: order_item
    desc: 订单明细表，记录订单中每个商品的详细信息，包括商品SKU(sku)、商品名字(pd_name)、购买数量(amount)、单价、实际支付总价等
  - name: merchant
    desc: 商户信息主表。存储了商户的名字(mname)、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息。
  - name: merchant_sub_account
    desc: 商户子账号表，记录商户子账号的基本信息，包括子账号ID(account_id)、子账号名字(contact)、子账号的最后登录时间(login_time)
  - name: admin
    desc: 大客户表(admin_type=0)，记录大客户ID(admin_id)、大客户名字(name_remakes)、大客户所属的销售员ID(saler_id)等
  - name: area
    desc: 运营服务区的基本信息，包括运营服务区编码(area_no)、运营服务区名字、运营服务区所属的大区编码(large_area_no)等
  - name: large_area
    desc: 大区表，记录运营服大区的基本信息，包括大区编码(large_area_no)、大区名字(large_area_name)等
  - name: products
    desc: 商品SPU表，记录商品的基本信息，包括商品ID(pd_id)、商品名字(pd_name)、商品类目ID(category_id)等
  - name: inventory
    desc: 商品SKU表，记录商品的详细信息，包括商品SKU(sku)、商品SPU ID(pd_id)、商品产地、商品规格(weight)等
  - name: category
    desc: 商品品类表，记录商品的类目信息，包括类目ID(id)、类目名字(category)、类目类型(type，=4表示水果类目)等
  - name: crm_bd_org
    desc: 销售组织架构表，记录销售人员的基本信息，包括销售人员ID(bd_id)、销售人员名字(bd_name)、销售人员所属的上级主管名字(parent_name)等
  - name: follow_up_relation
    desc: 商户销售私海关系表(reassign=0表示私海客户)，记录商户ID(m_id)、商户所属的销售员ID(admin_id)、商户所属的销售员名字(admin_name)等
  - name: follow_up_record
    desc: 记录商户被拜访的记录(有时也叫打卡记录），包括商户ID(m_id)、拜访人ID(admin_id)、拜访人名字(admin_name)、商户所属的运营服务区编码(area_no)等
  - name: after_sale_order
    desc: 售后单表，记录订单的售后申请的基本信息，包括售后订单编号(after_sale_order_no)、售后门店ID(m_id)、原订单编号(order_no)、售后商品SKU(sku)、售后状态(status)等
  - name: after_sale_proof
    desc: 售后明细表，记录售后单的处理明细情况，包括售后数量(quantity)、售后凭证图片(proof_pic)、最终售后金额(handle_num)、客服审核备注(apply_remark)等
  - name: delivery_plan
    desc: 订单配送、履约计划表，记录订单的配送信息，包括配送日期(delivery_time)、配送状态(status)、配送商品数量(quantity)等，一定要区分省心送订单和普通订单的履约件数统计逻辑
  - name: contact
    desc: 联系人收货地址表，存储商户的收货地址信息，每个contact_id代表一个配送点位。关联查询`merchant`.`m_id`和`delivery_plan`.`contact_id`，包含联系人信息、详细地址、配送仓库编号(store_no)、距离仓库距离(distance)等
  - name: shopping_cart
    desc: 门店的购物车，记录门店的购物车信息，包括门店id(m_id)、子账号id(account_id)、商品sku(sku)、商品数量(quantity)等
  - name: products_property_value
    desc: 商品属性值表，主要用来记录商品的品牌信息，包括商品ID(pd_id)、商品品牌名称(products_property_value, 当且仅当products_property_id=2时，表示商品的品牌)
  - name: merchant_label
    desc: 客户标签表，记录每个客户的标签。例如：过年营业、过年不营业、校区商户、非校区商户等
